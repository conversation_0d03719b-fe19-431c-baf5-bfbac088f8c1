var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
const {
    default: loc_grp_workflow,
} = require('./workflows/location_grp_workflow');
const db_resp = require('./utils/db_resp');
const {
    updateTimelineTaskTemplate,
} = require('./queues_v2/processors/email/templates/timeline_task_update_template');
const { allQueues } = require('./queues_v2/queues');
const users_model = require('./users_model');

class data_transform_model {
    updateSrvcReqsLocGrps(req) {
        let workflow = new loc_grp_workflow(this);
        workflow.triggerUpdateSrvcReqsLocGrps();
        return new Promise((resolve, reject) => {
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    async getOrUpdateIncorrectTimelineTaskEntries(mode) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_or_update_incorrect_timeline_task_entries(mode)
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }
                        var dbResp = new db_resp(
                            res[0].tms_get_or_update_incorrect_timeline_task_entries
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    updateTimelineTaskEntryCreatedByFromStatuschange(mode) {
        return new Promise(async (resolve, reject) => {
            let timelineTaskEntries =
                await this.getOrUpdateIncorrectTimelineTaskEntries(mode);
            if (!timelineTaskEntries.isSuccess()) {
                resolve(timelineTaskEntries);
                return;
            }
            let timelineRespData = JSON.parse(timelineTaskEntries.resp);
            let table = updateTimelineTaskTemplate(mode, timelineRespData);
            resolve(
                new sampleOperationResp(true, table, HttpStatus.StatusCodes.OK)
            );
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    getLocGrpModelData(data_transform_model) {
        return {
            ip_address: data_transform_model.ip_address,
            user_agent: data_transform_model.user_agent_,
            user_context: data_transform_model.user_context,
        };
    }

    /**
     * Get service types with counts of service requests that need cust_pincode updates
     * @returns {Promise<Object>} - Promise resolving to service types with counts
     */
    async getServiceTypesForCustPincodeUpdate() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db.tms_get_service_types_for_cust_pincode_update().then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_get_service_types_for_cust_pincode_update
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                dbResp.message || 'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    async getServiceTypesForPrvdrVerticalUpdate() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db.tms_get_service_types_for_prvdr_vertical_update().then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_get_service_types_for_prvdr_vertical_update
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                dbResp.message || 'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    /**
     * Update cust_pincode column for all service requests
     * @param {string} secretKey - Secret key for authorization
     * @returns {Promise<Object>} - Promise resolving to operation result
     */
    async updateCustPincodeForAllServiceRequests(secretKey) {
        return new Promise(async (resolve, reject) => {
            // Validate secret key
            const maintenceSecretApisKey =
                process.env.MAINTENANCE_SECRET_APIS_KEY;
            if (secretKey !== maintenceSecretApisKey) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Not authorized',
                        HttpStatus.StatusCodes.UNAUTHORIZED
                    )
                );
                return;
            }

            try {
                // Get service types and their counts
                const serviceTypesResp =
                    await this.getServiceTypesForCustPincodeUpdate();
                if (!serviceTypesResp.isSuccess()) {
                    resolve(serviceTypesResp);
                    return;
                }
                console.log(
                    'Datatransformmodel::serviceTypesResp',
                    serviceTypesResp
                );

                const serviceTypes = JSON.parse(serviceTypesResp.resp);
                if (!serviceTypes || serviceTypes.length === 0) {
                    resolve(
                        new sampleOperationResp(
                            true,
                            'No service requests found that need updating',
                            HttpStatus.StatusCodes.OK
                        )
                    );
                    return;
                }

                // Process each service type in batches
                const batchSize = 5000;
                let jobsAdded = 0;

                for (const serviceType of serviceTypes) {
                    const { srvc_type_id, count } = serviceType;
                    const totalBatches = Math.ceil(count / batchSize);

                    // Add a job for each batch
                    for (
                        let batchNumber = 0;
                        batchNumber < totalBatches;
                        batchNumber++
                    ) {
                        // Add job to the queue
                        allQueues.WIFY_UPDATE_SRVC_REQ_CUST_PINCODE.addJob(
                            {
                                serviceTypeId: srvc_type_id,
                                batchSize,
                                batchNumber: batchNumber + 1,
                                totalBatches,
                                data_transform_model_data:
                                    this.getModelDataForQueue(),
                            },
                            {
                                attempts: 10,
                                backoff: {
                                    type: 'exponential',
                                    delay: 5000, // Start with 5 seconds delay, then exponential backoff
                                },
                                removeOnComplete: true,
                                removeOnFail: false, // Keep failed jobs for inspection
                            }
                        );

                        jobsAdded++;
                    }
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify({
                            message:
                                'Started updating cust_pincode for all service requests',
                            serviceTypesCount: serviceTypes.length,
                            totalJobsAdded: jobsAdded,
                        }),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'Error in updateCustPincodeForAllServiceRequests:',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    /**
     * Update cust_pincode for a batch of service requests
     * @param {number} serviceTypeId - Service type ID
     * @param {number} batchSize - Number of records to update in this batch
     * @returns {Promise<Object>} - Promise resolving to batch update result
     */
    async updateSrvcReqCustPincodeBatch(serviceTypeId, batchSize) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db
                .tms_update_srvc_req_cust_pincode_batch(
                    serviceTypeId,
                    batchSize
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_update_srvc_req_cust_pincode_batch
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp.message || 'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    async updatePrvdrVerticalForAllServiceRequests(secretKey) {
        return new Promise(async (resolve, reject) => {
            const maintenceSecretApisKey =
                process.env.MAINTENANCE_SECRET_APIS_KEY;

            // Check authorization
            if (secretKey !== maintenceSecretApisKey) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Not authorized',
                        HttpStatus.StatusCodes.UNAUTHORIZED
                    )
                );
                return;
            }

            try {
                // Get service types that need updates
                const serviceTypesResp =
                    await this.getServiceTypesForPrvdrVerticalUpdate();
                if (!serviceTypesResp.isSuccess()) {
                    resolve(serviceTypesResp);
                    return;
                }

                const serviceTypes = JSON.parse(serviceTypesResp.resp);
                if (!serviceTypes || serviceTypes.length === 0) {
                    resolve(
                        new sampleOperationResp(
                            true,
                            'No service requests found that need updating',
                            HttpStatus.StatusCodes.OK
                        )
                    );
                    return;
                }

                // Process each service type in batches
                const batchSize = 100;
                let jobsAdded = 0;

                for (const serviceType of serviceTypes) {
                    const { srvc_type_id, count, vertical_id } = serviceType;
                    const totalBatches = Math.ceil(count / batchSize);

                    for (
                        let batchNumber = 0;
                        batchNumber < totalBatches;
                        batchNumber++
                    ) {
                        const jobData = {
                            query: {
                                srvc_type_id,
                                vertical_id,
                                ip_address: this.ip_address,
                                user_agent: this.user_agent_,
                                limit: batchSize,
                            },
                            data_transform_model_data:
                                this.getModelDataForQueue(),

                            // Include batch metadata in job data
                            batchSize,
                            batchNumber: batchNumber + 1,
                            totalBatches,
                        };

                        // Add job with proper name and options
                        allQueues.WIFY_UPDATE_PRVDR_VERTICAL_FOR_SRVC_REQS.addJob(
                            jobData, // Job data
                            {
                                attempts: 10,
                                backoff: {
                                    type: 'exponential',
                                    delay: 5000,
                                },
                                removeOnComplete: true,
                                removeOnFail: false,
                            }
                        );

                        console.log(
                            `data_transform_model::processVerticalUpdatesForServiceRequests:: Added job for service_type: ${srvc_type_id}, batch ${batchNumber + 1}/${totalBatches}`
                        );
                        jobsAdded++;
                    }
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify({
                            message:
                                'Started updating prvdr_vertical for all service requests',
                            serviceTypesCount: serviceTypes.length,
                            totalJobsAdded: jobsAdded,
                        }),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'Error in updatePrvdrVerticalForAllServiceRequests:',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    /**
     * Update prvdr_vertical for a batch of service requests
     * @param {number} serviceTypeId - Service type ID
     * @param {number} batchSize - Number of records to update in this batch
     * @returns {Promise<Object>} - Promise resolving to batch update result
     */
    async processUpdateSrvcReqPrvdrVerticalBatch(query) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db
                .tms_hlpr_update_srvc_reqs_vertical_by_srvc_type(
                    JSON.stringify(query)
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_hlpr_update_srvc_reqs_vertical_by_srvc_type
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp.message || 'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }
    // getSystemUserContext(org_id) {
    //     return {
    //         uuid: '37649b6d-d601-4206-b47f-0be4933bcd44',
    //         user_details: {
    //             org: {
    //                 id: org_id,
    //             },
    //             name: 'System Job Runner',
    //             role: 'system',
    //             is_system: true,
    //         },
    //     };
    // }

    getModelDataForQueue() {
        return {
            ip_addr: this.ip_address,
            user_agent: this.user_agent_,
            user_context: this.user_context,
        };
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }
}

module.exports.default = new data_transform_model();
