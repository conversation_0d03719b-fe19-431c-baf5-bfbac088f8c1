name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
on:
    pull_request:
        types: [opened, synchronize, reopened]
        #types: [opened, edited, reopened]

#Cancel prev CI if new commit come
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
    # Detect changes in different folders
    detect-changes:
        runs-on: ubuntu-latest
        outputs:
            frontend-changed: ${{ steps.changes.outputs.frontend }}
        steps:
            - name: Checkout repository
              uses: actions/checkout@v2
              with:
                  fetch-depth: 0

            - name: Detect changes
              id: changes
              run: |
                  # Get the list of changed files
                  if [ "${{ github.event_name }}" = "pull_request" ]; then
                      # For pull requests, compare against the base branch
                      CHANGED_FILES=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})
                  else
                      # For push events, compare against the previous commit
                      CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
                  fi

                  echo "Changed files:"
                  echo "$CHANGED_FILES"

                  # Check if frontend folder has changes
                  if echo "$CHANGED_FILES" | grep -q "^frontend/"; then
                      echo "frontend=true" >> $GITHUB_OUTPUT
                      echo "Frontend changes detected"
                  else
                      echo "frontend=false" >> $GITHUB_OUTPUT
                      echo "No frontend changes detected"
                  fi

    For-Auth:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./backend/Auth

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0

            - run: yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=4096

            - name: Run tests and generate coverage report
              run: |
                  # yarn jest --detectOpenHandles
                  yarn test
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=4096

    For-App:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./backend/App

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0

            - run: npm install
              env:
                  NODE_OPTIONS: --max_old_space_size=4096

            - name: Run tests and generate coverage report
              run: |
                  # yarn jest --detectOpenHandles
                  yarn test
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=4096

    For-Frontend:
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.frontend-changed == 'true'
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 20
              uses: actions/setup-node@v2
              with:
                  node-version: 20

            - run: yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=4096

            - name: Use Node.js 16.15.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.15.0

            - name: Run tests and generate coverage report
              run: |
                  yarn test-units
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192

    Status-Checks-For-Jest:
        runs-on: ubuntu-latest
        needs: [detect-changes, For-Auth, For-App, For-Frontend]
        if: ${{ always() }}
        steps:
            - name: Check Previous Jobs
              id: check-request
              run: |
                  # Always check For-Auth and For-App
                  if [[ ${{ needs.For-App.result }} == 'failure' || ${{ needs.For-Auth.result }} == 'failure' ]]; then
                    echo "Auth or App Jest checks failed."
                    exit 1
                  fi

                  # Only check For-Frontend if it was supposed to run
                  if [[ ${{ needs.detect-changes.outputs.frontend-changed }} == 'true' ]]; then
                    if [[ ${{ needs.For-Frontend.result }} == 'failure' ]]; then
                      echo "Frontend Jest checks failed."
                      exit 1
                    fi
                  fi

                  echo "All required Jest checks passed."

            - name: Check Status
              run: echo "Failed"
              if: steps.check-request.outcome == 'Failure'
