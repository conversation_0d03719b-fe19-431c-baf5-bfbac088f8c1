name: Stale Branch Detector

on:
    pull_request:
        types: [closed]
        # Only trigger when PR is merged (not just closed)
    workflow_dispatch: # Allows manual execution from GitHub Actions UI

# Cancel previous runs if new PR is merged
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
    detect-stale-branches:
        name: Find Branches Older Than 15 Days
        runs-on: ubuntu-latest
        # Run when PR is merged OR when manually triggered
        if: github.event.pull_request.merged == true || github.event_name == 'workflow_dispatch'

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0 # Fetch all branches and history

            - name: Find stale merged branches
              id: find-stale
              run: |
                  echo "🔍 Searching for merged branches older than 15 days..."
                  echo "📅 Current date: $(date)"

                  # Show trigger context
                  if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
                      echo "🚀 Triggered manually by: ${{ github.actor }}"
                  else
                      echo "🔄 Triggered by PR #${{ github.event.pull_request.number }} merge"
                      echo "📝 Merged branch: ${{ github.event.pull_request.head.ref }}"
                  fi
                  echo ""

                  # Get all remote branches
                  git fetch --all

                  # Calculate date 15 days ago
                  CUTOFF_DATE=$(date -d '15 days ago' '+%Y-%m-%d')
                  CUTOFF_TIMESTAMP=$(date -d '15 days ago' '+%s')

                  echo "📊 Cutoff date: $CUTOFF_DATE"
                  echo "🎯 Logic: Only checking branches that have been merged to main/master"
                  echo ""

                  # Determine the main branch name
                  MAIN_BRANCH=""
                  if git show-ref --verify --quiet refs/remotes/origin/main; then
                      MAIN_BRANCH="origin/main"
                      echo "📌 Main branch detected: main"
                  elif git show-ref --verify --quiet refs/remotes/origin/master; then
                      MAIN_BRANCH="origin/master"
                      echo "📌 Main branch detected: master"
                  else
                      echo "❌ Error: Could not find main or master branch"
                      exit 1
                  fi
                  echo ""

                  echo "🌿 Analyzing merged branches..."

                  STALE_BRANCHES=""
                  STALE_COUNT=0
                  TOTAL_MERGED_BRANCHES=0
                  ACTIVE_BRANCHES=0

                  # Get all remote branches except main/master
                  for branch in $(git branch -r --format='%(refname:short)' | grep -v 'HEAD' | grep -v "$MAIN_BRANCH" | sort); do
                      BRANCH_NAME=$(echo "$branch" | sed 's/origin\///')

                      # Skip if this is the main branch
                      if [ "$BRANCH_NAME" = "main" ] || [ "$BRANCH_NAME" = "master" ]; then
                          continue
                      fi

                      # Check if this branch has been merged into main
                      MERGE_BASE=$(git merge-base "$branch" "$MAIN_BRANCH" 2>/dev/null || echo "")
                      BRANCH_HEAD=$(git rev-parse "$branch" 2>/dev/null || echo "")

                      if [ -n "$MERGE_BASE" ] && [ -n "$BRANCH_HEAD" ] && [ "$MERGE_BASE" = "$BRANCH_HEAD" ]; then
                          # Branch is fully merged
                          TOTAL_MERGED_BRANCHES=$((TOTAL_MERGED_BRANCHES + 1))

                          # Find when this branch was merged (look for merge commit in main)
                          MERGE_COMMIT=$(git log --merges --oneline --grep="$BRANCH_NAME" "$MAIN_BRANCH" --since="6 months ago" | head -1 | cut -d' ' -f1 2>/dev/null || echo "")

                          if [ -z "$MERGE_COMMIT" ]; then
                              # Fallback: use the last commit date of the branch
                              MERGE_DATE=$(git log -1 --format="%ci" "$branch" 2>/dev/null || echo "")
                              MERGE_SOURCE="branch last commit"
                          else
                              # Use the merge commit date
                              MERGE_DATE=$(git log -1 --format="%ci" "$MERGE_COMMIT" 2>/dev/null || echo "")
                              MERGE_SOURCE="merge commit"
                          fi

                          if [ -n "$MERGE_DATE" ]; then
                              # Convert to timestamp for comparison
                              MERGE_TIMESTAMP=$(date -d "$MERGE_DATE" '+%s' 2>/dev/null || echo "0")

                              # Check if merge was more than 15 days ago
                              if [ "$MERGE_TIMESTAMP" -lt "$CUTOFF_TIMESTAMP" ]; then
                                  STALE_COUNT=$((STALE_COUNT + 1))
                                  DAYS_OLD=$(( ($(date '+%s') - MERGE_TIMESTAMP) / 86400 ))

                                  echo "🗓️  Branch: $BRANCH_NAME"
                                  echo "   Merged: $MERGE_DATE ($MERGE_SOURCE)"
                                  echo "   Days since merge: $DAYS_OLD days"
                                  echo "   Status: ✅ Merged & Stale"
                                  echo ""

                                  # Add to stale branches list
                                  if [ -z "$STALE_BRANCHES" ]; then
                                      STALE_BRANCHES="$BRANCH_NAME ($DAYS_OLD days since merge)"
                                  else
                                      STALE_BRANCHES="$STALE_BRANCHES\n$BRANCH_NAME ($DAYS_OLD days since merge)"
                                  fi
                              else
                                  echo "⏰ Branch: $BRANCH_NAME (recently merged, keeping)"
                              fi
                          fi
                      else
                          # Branch is not merged (still active)
                          ACTIVE_BRANCHES=$((ACTIVE_BRANCHES + 1))
                          LAST_COMMIT_DATE=$(git log -1 --format="%ci" "$branch" 2>/dev/null || echo "")
                          echo "🔄 Branch: $BRANCH_NAME (active - not merged yet)"
                      fi
                  done

                  echo "📈 Summary:"
                  echo "   Total merged branches: $TOTAL_MERGED_BRANCHES"
                  echo "   Active branches (not merged): $ACTIVE_BRANCHES"
                  echo "   Stale merged branches: $STALE_COUNT"
                  echo ""

                  if [ "$STALE_COUNT" -gt 0 ]; then
                      echo "⚠️  STALE MERGED BRANCHES DETECTED!"
                      echo "The following branches were merged more than 15 days ago:"
                      echo ""
                      echo -e "$STALE_BRANCHES"
                      echo ""
                      echo "💡 These branches are safe to delete as they have been merged to main."
                      echo "🔧 You can delete them using: git push origin --delete <branch-name>"

                      # Set output for potential use in other steps
                      echo "stale_count=$STALE_COUNT" >> $GITHUB_OUTPUT
                      echo "has_stale_branches=true" >> $GITHUB_OUTPUT
                  else
                      echo "✅ No stale merged branches found!"
                      if [ "$TOTAL_MERGED_BRANCHES" -gt 0 ]; then
                          echo "All merged branches were merged within the last 15 days."
                      else
                          echo "No merged branches found to analyze."
                      fi
                      if [ "$ACTIVE_BRANCHES" -gt 0 ]; then
                          echo "📝 Note: $ACTIVE_BRANCHES active branches are still being worked on."
                      fi
                      echo "stale_count=0" >> $GITHUB_OUTPUT
                      echo "has_stale_branches=false" >> $GITHUB_OUTPUT
                  fi

            - name: Create summary comment
              if: steps.find-stale.outputs.has_stale_branches == 'true' && github.event_name == 'pull_request'
              uses: actions/github-script@v7
              with:
                  script: |
                      const staleCount = '${{ steps.find-stale.outputs.stale_count }}';

                      const comment = `## 🌿 Stale Merged Branch Detection Report

                      **Triggered by:** PR #${{ github.event.pull_request.number }} merge
                      **Branch merged:** \`${{ github.event.pull_request.head.ref }}\`

                      ### 📊 Results
                      - **Stale merged branches found:** ${staleCount} branches merged >15 days ago
                      - **Analysis date:** ${new Date().toISOString().split('T')[0]}
                      - **Logic:** Only counting branches that have been merged to main

                      ### 💡 Recommendation
                      The stale branches listed in the workflow logs are **safe to delete** as they have been fully merged to main.

                      **Delete command:** \`git push origin --delete <branch-name>\`

                      **View detailed results:** [Workflow Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

                      ---
                      *This report was automatically generated after PR merge. Active branches (not yet merged) are preserved.*`;

                      github.rest.issues.createComment({
                        issue_number: ${{ github.event.pull_request.number }},
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        body: comment
                      });

            - name: No stale branches found
              if: steps.find-stale.outputs.has_stale_branches == 'false'
              run: |
                  echo "🎉 Great news! No stale branches detected."
                  echo "All branches have recent activity within the last 15 days."

                  if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
                      echo ""
                      echo "✅ Manual scan completed successfully."
                      echo "Your repository branches are all up-to-date!"
                  fi
