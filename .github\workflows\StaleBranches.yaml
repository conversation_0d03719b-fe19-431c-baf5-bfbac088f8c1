name: Stale Branch Detector

on:
    pull_request:
        types: [closed]
        # Only trigger when PR is merged (not just closed)
    workflow_dispatch: # Allows manual execution from GitHub Actions UI

# Cancel previous runs if new PR is merged
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
    detect-stale-branches:
        name: Find Branches Older Than 15 Days
        runs-on: ubuntu-latest
        # Run when PR is merged OR when manually triggered
        if: github.event.pull_request.merged == true || github.event_name == 'workflow_dispatch'

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0 # Fetch all branches and history

            - name: Find stale branches
              id: find-stale
              run: |
                  echo "🔍 Searching for branches older than 15 days..."
                  echo "📅 Current date: $(date)"

                  # Show trigger context
                  if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
                      echo "🚀 Triggered manually by: ${{ github.actor }}"
                  else
                      echo "🔄 Triggered by PR #${{ github.event.pull_request.number }} merge"
                      echo "📝 Merged branch: ${{ github.event.pull_request.head.ref }}"
                  fi
                  echo ""

                  # Get all remote branches
                  git fetch --all

                  # Calculate date 15 days ago
                  CUTOFF_DATE=$(date -d '15 days ago' '+%Y-%m-%d')
                  CUTOFF_TIMESTAMP=$(date -d '15 days ago' '+%s')

                  echo "📊 Cutoff date: $CUTOFF_DATE"
                  echo ""

                  # Get all branches (remote and local) with their last commit dates
                  echo "🌿 Analyzing all branches..."

                  STALE_BRANCHES=""
                  STALE_COUNT=0
                  TOTAL_BRANCHES=0

                  # Get all remote branches
                  for branch in $(git branch -r --format='%(refname:short)' | grep -v 'HEAD' | sort); do
                      TOTAL_BRANCHES=$((TOTAL_BRANCHES + 1))
                      
                      # Get the last commit date for this branch
                      LAST_COMMIT_DATE=$(git log -1 --format="%ci" "$branch" 2>/dev/null || echo "")
                      
                      if [ -n "$LAST_COMMIT_DATE" ]; then
                          # Convert to timestamp for comparison
                          LAST_COMMIT_TIMESTAMP=$(date -d "$LAST_COMMIT_DATE" '+%s' 2>/dev/null || echo "0")
                          
                          # Check if branch is older than 15 days
                          if [ "$LAST_COMMIT_TIMESTAMP" -lt "$CUTOFF_TIMESTAMP" ]; then
                              STALE_COUNT=$((STALE_COUNT + 1))
                              BRANCH_NAME=$(echo "$branch" | sed 's/origin\///')
                              DAYS_OLD=$(( ($(date '+%s') - LAST_COMMIT_TIMESTAMP) / 86400 ))
                              
                              echo "🗓️  Branch: $BRANCH_NAME"
                              echo "   Last commit: $LAST_COMMIT_DATE"
                              echo "   Days old: $DAYS_OLD days"
                              echo ""
                              
                              # Add to stale branches list
                              if [ -z "$STALE_BRANCHES" ]; then
                                  STALE_BRANCHES="$BRANCH_NAME ($DAYS_OLD days old)"
                              else
                                  STALE_BRANCHES="$STALE_BRANCHES\n$BRANCH_NAME ($DAYS_OLD days old)"
                              fi
                          fi
                      fi
                  done

                  echo "📈 Summary:"
                  echo "   Total branches analyzed: $TOTAL_BRANCHES"
                  echo "   Stale branches found: $STALE_COUNT"
                  echo ""

                  if [ "$STALE_COUNT" -gt 0 ]; then
                      echo "⚠️  STALE BRANCHES DETECTED!"
                      echo "The following branches are older than 15 days:"
                      echo ""
                      echo -e "$STALE_BRANCHES"
                      echo ""
                      echo "💡 Consider reviewing these branches for potential cleanup."
                      
                      # Set output for potential use in other steps
                      echo "stale_count=$STALE_COUNT" >> $GITHUB_OUTPUT
                      echo "has_stale_branches=true" >> $GITHUB_OUTPUT
                  else
                      echo "✅ No stale branches found! All branches are recent."
                      echo "stale_count=0" >> $GITHUB_OUTPUT
                      echo "has_stale_branches=false" >> $GITHUB_OUTPUT
                  fi

            - name: Create summary comment
              if: steps.find-stale.outputs.has_stale_branches == 'true' && github.event_name == 'pull_request'
              uses: actions/github-script@v7
              with:
                  script: |
                      const staleCount = '${{ steps.find-stale.outputs.stale_count }}';

                      const comment = `## 🌿 Stale Branch Detection Report

                      **Triggered by:** PR #${{ github.event.pull_request.number }} merge
                      **Branch merged:** \`${{ github.event.pull_request.head.ref }}\`

                      ### 📊 Results
                      - **Stale branches found:** ${staleCount} branches older than 15 days
                      - **Analysis date:** ${new Date().toISOString().split('T')[0]}

                      ### 💡 Recommendation
                      Consider reviewing the stale branches listed in the workflow logs for potential cleanup.

                      **View detailed results:** [Workflow Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

                      ---
                      *This report was automatically generated after PR merge.*`;

                      github.rest.issues.createComment({
                        issue_number: ${{ github.event.pull_request.number }},
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        body: comment
                      });

            - name: No stale branches found
              if: steps.find-stale.outputs.has_stale_branches == 'false'
              run: |
                  echo "🎉 Great news! No stale branches detected."
                  echo "All branches have recent activity within the last 15 days."

                  if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
                      echo ""
                      echo "✅ Manual scan completed successfully."
                      echo "Your repository branches are all up-to-date!"
                  fi
