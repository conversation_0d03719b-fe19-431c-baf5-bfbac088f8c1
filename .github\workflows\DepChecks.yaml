name: DepCheck

on:
    workflow_dispatch: # Allows manual execution from GitHub Actions UI
    push:
        branches:
            - main

jobs:
    # Detect changes in different folders
    detect-changes:
        runs-on: ubuntu-latest
        outputs:
            auth-changed: ${{ steps.changes.outputs.auth }}
            app-changed: ${{ steps.changes.outputs.app }}
            frontend-changed: ${{ steps.changes.outputs.frontend }}
        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Detect changes
              id: changes
              run: |
                  # Get the list of changed files
                  if [ "${{ github.event_name }}" = "push" ]; then
                      # For push events, compare against the previous commit
                      CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
                  elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
                      # For manual dispatch, check all files (run everything)
                      echo "Manual workflow dispatch - running all checks"
                      echo "auth=true" >> $GITHUB_OUTPUT
                      echo "app=true" >> $GITHUB_OUTPUT
                      echo "frontend=true" >> $GITHUB_OUTPUT
                      exit 0
                  else
                      # For other events, compare against the previous commit
                      CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
                  fi

                  echo "Changed files:"
                  echo "$CHANGED_FILES"
                  echo ""
                  echo "Repository structure:"
                  echo "- frontend/ (android-app, react-app1)"
                  echo "- backend/ (App, Auth, Serverless, forwarder, AppForwarder)"
                  echo ""

                  # Check backend/Auth changes
                  if echo "$CHANGED_FILES" | grep -q "^backend/Auth/"; then
                      echo "auth=true" >> $GITHUB_OUTPUT
                      echo "✓ Backend/Auth changes detected"
                  else
                      echo "auth=false" >> $GITHUB_OUTPUT
                      echo "✗ No Backend/Auth changes"
                  fi

                  # Check backend/App changes
                  if echo "$CHANGED_FILES" | grep -q "^backend/App/"; then
                      echo "app=true" >> $GITHUB_OUTPUT
                      echo "✓ Backend/App changes detected"
                  else
                      echo "app=false" >> $GITHUB_OUTPUT
                      echo "✗ No Backend/App changes"
                  fi

                  # Check frontend changes (any subfolder)
                  if echo "$CHANGED_FILES" | grep -q "^frontend/"; then
                      echo "frontend=true" >> $GITHUB_OUTPUT
                      echo "✓ Frontend changes detected"

                      # Show which frontend components changed
                      if echo "$CHANGED_FILES" | grep -q "^frontend/android-app/"; then
                          echo "  - Android app changes detected"
                      fi
                      if echo "$CHANGED_FILES" | grep -q "^frontend/react-app1/"; then
                          echo "  - React app changes detected"
                      fi
                  else
                      echo "frontend=false" >> $GITHUB_OUTPUT
                      echo "✗ No Frontend changes"
                  fi

    # Backend dependency checks - only run for changed components
    security_scan:
        name: Run Dependency Checks
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.auth-changed == 'true' || needs.detect-changes.outputs.app-changed == 'true'

        strategy:
            matrix:
                project:
                    - {
                          name: 'Auth',
                          path: './backend/Auth',
                          condition: 'auth-changed',
                      }
                    - {
                          name: 'App',
                          path: './backend/App',
                          condition: 'app-changed',
                      }

        steps:
            - name: Check if this project should run
              id: should-run
              run: |
                  PROJECT_CONDITION="${{ matrix.project.condition }}"
                  if [ "$PROJECT_CONDITION" = "auth-changed" ] && [ "${{ needs.detect-changes.outputs.auth-changed }}" = "true" ]; then
                      echo "should_run=true" >> $GITHUB_OUTPUT
                      echo "✅ Running dependency check for ${{ matrix.project.name }} (changes detected)"
                  elif [ "$PROJECT_CONDITION" = "app-changed" ] && [ "${{ needs.detect-changes.outputs.app-changed }}" = "true" ]; then
                      echo "should_run=true" >> $GITHUB_OUTPUT
                      echo "✅ Running dependency check for ${{ matrix.project.name }} (changes detected)"
                  else
                      echo "should_run=false" >> $GITHUB_OUTPUT
                      echo "⏭️ Skipping dependency check for ${{ matrix.project.name }} (no changes detected)"
                  fi

            - name: Checkout Repository
              if: steps.should-run.outputs.should_run == 'true'
              uses: actions/checkout@v4

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0

            - name: Ensure Lock File Exists
              working-directory: ${{ matrix.project.path }}
              run: |
                  if [ ! -f package-lock.json ]; then
                    echo "Generating package-lock.json..."
                    npm install --package-lock-only
                  fi

            - name: Install Dependencies
              working-directory: ${{ matrix.project.path }}
              run: npm ci

            - name: Run OWASP Dependency-Check
              uses: dependency-check/Dependency-Check_Action@main
              with:
                  project: '${{ matrix.project.name }}'
                  format: 'HTML'
                  out: '${{ matrix.project.path }}/dependency-check-report'

            - name: Upload Dependency-Check Report
              uses: actions/upload-artifact@v4
              with:
                  name: dependency-check-report-${{ matrix.project.name }}
                  path: ${{ matrix.project.path }}/dependency-check-report

    send_slack_report:
        name: Send Slack Message
        runs-on: ubuntu-latest
        needs: security_scan

        steps:
            - name: Generate Slack Message
              env:
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
                  SLACK_CHANNEL_ID: 'C08HTNPESP8' # Replace with your actual Slack Channel ID
                  GITHUB_RUN_URL: 'https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}'
              run: |
                  MESSAGE="📢 *Dependency Check Reports Available* \n\n\
                  ##############################\n\
                  🔍 *Security Scan Results:* \n\
                  ##############################\n\n\
                  The latest dependency check reports are available in GitHub Actions. \n\n"

                  REPORTS_LIST=""

                  for PROJECT in "Auth" "App"; do
                      REPORTS_LIST+="🔹 *$PROJECT Report:* [View Report]($GITHUB_RUN_URL)\n"
                  done

                  MESSAGE+="$REPORTS_LIST 🚀"

                  curl -X POST https://slack.com/api/chat.postMessage \
                    -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
                    -H "Content-Type: application/json" \
                    --data '{
                      "channel": "'"$SLACK_CHANNEL_ID"'",
                      "text": "'"$MESSAGE"'"
                    }'
