var express = require('express');
var router = express.Router();
const { default: model } = require('../api_models/data_transform_model');
const {
    getSbtskWorkflowModel,
} = require('../api_models/queues_v2/processors/helpers/sbtsk_workflow_helper');

// Routes for data transformation operations

router.get('/update-srvc-reqs-loc-grps/:secret_key', function (req, res, next) {
    setParamsToModel(req);
    let secret_key = req.params.secret_key;
    let srvcReqsLocGrpsKey = process.env.SYNC_SRVC_REQS_LOC_GRPS_KEY;
    if (secret_key == srvcReqsLocGrpsKey) {
        model.updateSrvcReqsLocGrps(req).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    } else {
        res.status(401).send('Not Authorized');
    }
});

const validateMiddlewareSecretAPIKey = (req, res, next) => {
    setParamsToModel(req);
    let maintenceSecretApisKey = process.env.MAINTENANCE_SECRET_APIS_KEY;
    let secret_key = req.params.secret_key;
    if (secret_key != maintenceSecretApisKey) {
        res.status(401).send('Not Authorized');
    }
    return next();
};

router.get(
    '/update-timeline-task-entry/:secret_key/:mode?',
    validateMiddlewareSecretAPIKey,
    function (req, res, next) {
        setParamsToModel(req);
        let current_mode = req.params.mode;
        if (current_mode == undefined || current_mode == '') {
            res.status(400).send('Please provide mode in the URL');
        }
        if (current_mode != 'test' && current_mode != 'run') {
            res.status(400).send(
                'Please ensure that you have entered the correct mode in the URL to get the desired functionality!'
            );
        }
        let isTestMode = false;
        if (current_mode == 'test') {
            isTestMode = true;
        }
        model
            .updateTimelineTaskEntryCreatedByFromStatuschange(isTestMode)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

/**
 * Update cust_pincode column for all service requests by extracting data from form_data JSON
 * This endpoint triggers batch jobs to update the cust_pincode column for all service requests
 * Jobs are created for every 10k requests of each unique service type
 */
router.get(
    '/update-cust-pincode/:secret_key',
    validateMiddlewareSecretAPIKey,
    async function (req, res, next) {
        setParamsToModel(req);
        try {
            const secretKey = req.params.secret_key;
            const operationResp =
                await model.updateCustPincodeForAllServiceRequests(secretKey);
            res.status(operationResp.httpStatus).send(operationResp.resp);
        } catch (error) {
            console.error('Error in update-cust-pincode endpoint:', error);
            res.status(500).send('Internal server error');
        }
    }
);

router.get(
    '/update-prvdr-vertical/:secret_key',
    validateMiddlewareSecretAPIKey,
    async function (req, res, next) {
        setParamsToModel(req);
        try {
            const secretKey = req.params.secret_key;
            const operationResp =
                await model.updatePrvdrVerticalForAllServiceRequests(secretKey);
            res.status(operationResp.httpStatus).send(operationResp.resp);
        } catch (error) {
            console.error('Error in update-prvdr-vertical endpoint:', error);
            res.status(500).send('Internal server error');
        }
    }
);

// router.get('/regenrate-bridges-for-request',async function(req,res,next){

//   let successEnteries = [],failedEnteries = []

//   let batch_data = req.body.batch_data
//   if(batch_data.length > 0) {

//     for(let i=0; i<batch_data.length; i++) {
//       const single_data = batch_data[i]
//       const {srvc_req_id,sbtsk_type_id,srvc_type_id,sbtsk_id,assignee_id} = single_data
//         const subtask_model = getSubtaskModel(req,srvc_req_id,sbtsk_type_id,srvc_type_id)
//         const sbtskWrkFlowModel = getSbtskWorkflowModel(subtask_model);
//         const query = {
//           org_id : 262,
//           srvc_type_id,
//           sbtsk_type_id
//         }
//         try{
//           let result = await sbtskWrkFlowModel.processSbtskReassignmentFrBridgeRecreate(query,srvc_req_id,sbtsk_id,assignee_id,subtask_model.db,true)
//           if(result) {
//             successEnteries.push(single_data)
//           } else {
//             failedEnteries.push(single_data)
//           }
//         } catch(e) {
//           failedEnteries.push(single_data)
//         }
//     }
//     res.status(200).json({successEnteries,failedEnteries})
//   } else {
//     res.status(200)
//   }

// })

const setParamsToModel = (req) => {
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
};

// const getSubtaskModel = (req,srvc_req_id,sbtsk_type_id,srvc_type_id) => {
//   const subtask_model = require("../api_models/subtasks_model").getInstance();
//   subtask_model.database =  req.app.get('db');
//   subtask_model.ip_addr =  req.ip;
//   subtask_model.user_agent =  req.get('User-Agent');
//   subtask_model.user_context =   {
//       "uuid" : '321f79b0-9ace-420f-a25d-f7c0ea958945',
//       "user_details" : {
//           "org" : {
//               "id" : 262
//           }
//       }
//   };
//   // Specific for service types
//   subtask_model.srvc_req_id = srvc_req_id
//   subtask_model.sbtsk_type_id = sbtsk_type_id
//   subtask_model.srvc_type_id = srvc_type_id
//   return subtask_model;
// }

module.exports = router;
