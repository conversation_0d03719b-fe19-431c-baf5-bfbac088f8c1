var sampleOperationResp = require('../api_models/utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../api_models/utils/db_resp');
const users_model = require('./users_model');
const {
    DEL_LOGIN_TOKEN_IN_CACHE,
    SET_USERS_DETAIL_LIST_IN_CACHE,
    GET_USERS_DETAIL_LIST_IN_CACHE,
} = require('./utils/redis_helpers');
const pagination_filters_utils = require('./utils/pagination_filters_utils');

const JSONStream = require('JSONStream');
const jsonToCsv = require('json-to-csv-stream');
const fs = require('fs');
const path = require('path');
const { allQueues } = require('./queues_v2/queues');
const isEmpty = require('is-empty');
// const { EMAIL } = require('../../Auth/api_models/constants');
const { EMAIL } = require('./constants');
const { moduleKeys } = require('./utils/helper');
const {
    dumpExportReqCounter,
    dumpExportFailureCounter,
    dumpExportStatus,
    dumpExportsCounter,
    dumpExportSuccessCounter,
} = require('./utils/metrics');
class user_model {
    getCapacityConfiguration(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            //  console.log('getViewDataFrUserForm ',form_data)
            this.db.tms_user_get_capacity_config_data(form_data).then(
                (res) => {
                    var userViewResp = new db_resp(
                        res[0].tms_user_get_capacity_config_data
                    );

                    if (!userViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(userViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    exportUsersByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_addr'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            const jobData = { requester, filters_ };
            allQueues.WIFY_USERS_EXPORT_BY_EMAIL.addJob(jobData);
            dumpExportReqCounter.inc({
                module: moduleKeys.users,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processUsersExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbReplica || this.db;
                if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }

                dbObj
                    .tms_get_users_req_dumps(requesterInfo, filters_, {
                        stream: true,
                    })
                    .then(
                        (stream) => {
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'user_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `User dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.users,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_users_req_dumps',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.users,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.users,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    // Method to get restriction settings
    async checkUserRestrictions(formData) {
        try {
            let dbResp = await this.db.tms_hlpr_get_setting_details(
                formData,
                'RESTRICTIONS'
            );
            let keyStore = dbResp[0].tms_hlpr_get_setting_details;
            if (keyStore.code == 'success') {
                let { restrict_user_from_changing_password } =
                    keyStore?.data?.form_data;
                if (restrict_user_from_changing_password) {
                    return true;
                } else {
                    return false;
                }
            } else {
                // password restirction is not configured
                return false;
            }
        } catch (error) {
            console.error('Error checking user restrictions:', error);
            throw error;
        }
    }

    // Method to check user password policy
    async checkPasswordPolicy(orgId, password) {
        try {
            let dbResp = await this.db.tms_hlpr_check_usr_password_policy(
                orgId,
                password
            );
            let keyStore = dbResp[0].tms_hlpr_check_usr_password_policy;
            return keyStore;
        } catch (error) {
            console.error('Error checking password policy:', error);
            throw error;
        }
    }

    // Method to update the user's password
    async updatePassword(userId, indId, newPassword, formData) {
        try {
            let dbResp = await this.db.tms_create_user_identity(
                userId,
                EMAIL,
                indId,
                newPassword,
                formData
            );
            let keyStore = dbResp[0].tms_create_user_identity;
            if (keyStore > 0) {
                return true;
            } else {
                return false;
            }
        } catch (error) {
            console.error('Error updating password:', error);
            throw error;
        }
    }

    async changeUserPassword(query) {
        return new Promise(async (resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);
            var old_pass = query.user_old_password;
            var new_pass = query.user_new_password;
            var c_new_pass = query.user_confirm_pass;
            var usr_id = query.usr_id;
            var org_id = query.org_id;
            var ind_id = null;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            if (new_pass != c_new_pass) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'User new Password & confirm new password do not match',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
                return;
            }

            // get the email identity record
            try {
                let userDbResp =
                    await this.db.tms_get_user_details_by_id(usr_id);
                let userObj = userDbResp[0].tms_get_user_details_by_id;
                ind_id = userObj.data.email;
            } catch (e) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Internal server Error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            if (ind_id != null) {
                try {
                    // Check if user is restricted from changing password
                    const isPassRestrictionEnabled =
                        await this.checkUserRestrictions(form_data);
                    if (isPassRestrictionEnabled) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'You are restricted for this',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                        return;
                    }

                    // If not restricted, check the user's password policy (valid password pattern)
                    const policyCheckResponse = await this.checkPasswordPolicy(
                        org_id,
                        new_pass
                    );
                    if (policyCheckResponse.status == true) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                policyCheckResponse?.message,
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                        return;
                    }

                    // verify user with old password then change password
                    let dbResp =
                        await this.db.tms_hlpr_get_email_identities_cred_resp(
                            usr_id,
                            EMAIL,
                            ind_id
                        );
                    let keyStore =
                        dbResp[0].tms_hlpr_get_email_identities_cred_resp;

                    const { verifyPassword } = require('./utils/authrizor');
                    let isPasswordMatched = await verifyPassword(
                        old_pass,
                        keyStore.ind_key
                    );

                    if (isPasswordMatched == false) {
                        let resolve_http_code = HttpStatus.StatusCodes.CONFLICT;
                        let message = 'Old password is incorrect';
                        resolve(
                            new sampleOperationResp(
                                false,
                                message,
                                resolve_http_code
                            )
                        );
                        return;
                    } else if (old_pass == new_pass) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Old Password & New Password are same!',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                        return;
                    } else {
                        const { hashPassword } = require('./utils/authrizor');
                        // hash new password
                        const hashedNewPass = await hashPassword(new_pass);
                        // update new password in db
                        const passwordUpdated = await this.updatePassword(
                            usr_id,
                            ind_id,
                            hashedNewPass,
                            form_data
                        );
                        if (passwordUpdated) {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    'success',
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                        }
                    }
                } catch (e) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Internal server Error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                }
            } else {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Internal server Error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getUserOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_user_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_user_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSingleEntry(query, entry_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_user_details(entry_id).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(res[0].tms_get_user_details);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        this.getViewDataFrUserForm({}).then((operationResp) => {
                            if (operationResp.isSuccess()) {
                                var finalResp = JSON.parse(operationResp.resp);
                                // console.log(dbResp.data);
                                finalResp.form_data = dbResp.data;
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(finalResp),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            } else {
                                resolve(operationResp);
                            }
                        });
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getViewDataFrUserForm(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            //  console.log('getViewDataFrUserForm ',form_data)
            this.db.tms_user_getview_data(form_data).then(
                (res) => {
                    var userViewResp = new db_resp(
                        res[0].tms_user_getview_data
                    );

                    if (!userViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(userViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdateBatch(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = isEmpty(query?.organization_id)
                ? users_model.getOrgId(this.userContext)
                : query.organization_id;
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['batch_data'] = query.batch_data;

            var form_data = JSON.stringify(query);
            // console.log("form_data",form_data);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            // console.log("Form data to function",form_data);
            this.db.tms_create_user_batch(form_data).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_create_user_batch);

                    if (dbResp.code == 'usr_identity_already_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'User email and phone should be unique for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (dbResp.code == 'usr_roles_not_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'User role IDs not exists for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (
                        dbResp.code ==
                        'password_does_not_match_organization_policy'
                    ) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                dbResp.data,
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                        return;
                    } else if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        const batchData = query?.batch_data;
                        const functionToFilterDeactivatedUser = (data) => {
                            if (data.is_active != undefined) {
                                if (typeof data.is_active == 'boolean') {
                                    return data.is_active == false;
                                } else {
                                    return data.is_active.toLowerCase() == 'no';
                                }
                            }
                            return false;
                        };
                        const userIdArray = batchData
                            .filter(functionToFilterDeactivatedUser)
                            .map((data) => data.user_id);
                        if (userIdArray.length > 0) {
                            allQueues.WIFY_LOGOUT_DEACTIVATED_USERS.addJob(
                                userIdArray
                            );
                        }
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdateUser(query, entry_id = null) {
        // check if form fields is empty
        // Yes --> return Bad request (please fill * mandatory field)

        // check if email and phone exists in same org_id from usr_identities table
        // Yes --> return conflict (User email and phone should be unique for the same organization)

        // insert into user table
        // insert into usr identities table

        // check user_email is not empty
        // Yes --> set user_ind_type EMAIL

        // check user_mobile is not empty
        // Yes --> set user_ind_type MOBILE_NUM

        // insert into usr roles table

        // if insert successfully
        // Yes --> return HttpStatus.StatusCodes.OK and entry_id
        // No --> return Internal server error status= false and message = Internal_error

        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['usr_id'] = users_model.getUUID(this.userContext);
            var form_data = JSON.stringify(query);
            // console.log("form_data",form_data)
            if (!this.validateUserForm(form_data)) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Please fill mandatory * field.',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
                return;
            }
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db.tms_create_user(form_data, entry_id).then(
                (res) => {
                    var userViewResp = new db_resp(res[0].tms_create_user);

                    if (userViewResp.code == 'usr_identity_already_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'User name, email, phone and code should be unique for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (userViewResp.code == 'usr_roles_not_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'User role IDs not exists for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (
                        userViewResp.code ==
                        'password_does_not_match_organization_policy'
                    ) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                userViewResp?.data?.message,
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                        return;
                    } else if (!userViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        if (!query.is_active) {
                            if (!this.db) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'DB not found',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );
                                return;
                            }

                            allQueues.WIFY_LOGOUT_DEACTIVATED_USERS.addJob([
                                entry_id,
                            ]);
                        }
                        this.getUserDetailsAndSetUserDetailByIdInCache(
                            userViewResp.data.entry_id
                        );
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(userViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    async getUserDetailsAndSetUserDetailByIdInCache(
        user_id,
        userDetailsDbResp = undefined
    ) {
        try {
            let userDetailsResp =
                userDetailsDbResp ||
                (await this.db.tms_get_user_details_by_id(user_id))?.[0]
                    .tms_get_user_details_by_id.data;
            SET_USERS_DETAIL_LIST_IN_CACHE(user_id, userDetailsResp);
        } catch (error) {
            console.log('tms_get_user_details_by_id error', error);
        }
    }

    getUserDataById(query, entry_id, retrieveFrmRedisCache = false) {
        return new Promise(async (resolve, reject) => {
            const showInActive = query.showInActive;
            let redisResp;
            if (!showInActive) {
                redisResp = retrieveFrmRedisCache
                    ? await GET_USERS_DETAIL_LIST_IN_CACHE(entry_id)
                    : undefined;
            }

            // console.log("redisResp",redisResp);
            if (redisResp == undefined) {
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                this.db.tms_get_user_details_by_id(entry_id, showInActive).then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }
                        // console.log('reach')
                        var dbResp = new db_resp(
                            res[0].tms_get_user_details_by_id
                        );
                        // console.log('db', dbResp)
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            this.getUserDetailsAndSetUserDetailByIdInCache(
                                entry_id,
                                dbResp.data
                            );

                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
            } else {
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(redisResp),
                        HttpStatus.StatusCodes.OK
                    )
                );
            }
        });
    }

    getAllUsers(form) {
        if (Object.keys(form).length != 0) {
            var org_id = users_model.getOrgId(this.userContext);
            var pagination = JSON.parse(form.pagination);
            var page_no = pagination.current;
            var page_size = pagination.pageSize;
            var search_query = form.search_query;

            //Filters remove -1 in from array
            var filters = JSON.parse(form.filters);
            var filtersAllKeys = Object.keys(filters);
            for (var i = 0; i < filtersAllKeys.length; i++) {
                // if (filters[filtersAllKeys[i]][0] == '-1') {
                //     filters[filtersAllKeys[i]].pop();
                // }
                if (
                    filters[filtersAllKeys[i]]?.length > 0 &&
                    Array.isArray(filters[filtersAllKeys[i]]) &&
                    filters[filtersAllKeys[i]].includes('-1')
                ) {
                    delete filters[filtersAllKeys[i]];
                }
            }
            filters = JSON.stringify(filters);
        }

        // console.log("filters",filters);

        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_users(
                    org_id,
                    page_no,
                    page_size,
                    filters,
                    search_query
                )
                .then(
                    (res) => {
                        var usersResp = new db_resp(res[0].tms_get_users);

                        if (!usersResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(usersResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    updateFcmToken(form) {
        return new Promise((resolve, reject) => {
            if (!this.validateFcmFormData(form)) {
                return resolve(
                    new sampleOperationResp(
                        false,
                        'Invalid Data',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
            if (!this.db) {
                return resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
            var form_data = JSON.stringify(form);
            this.db.tms_update_fcm_token(form_data).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_update_fcm_token);
                    if (!dbResp.status) {
                        return resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    } else {
                        return resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    validateFcmFormData(form) {
        return form.uuid && form.hash && form.fcm_token;
    }

    validateUserForm(form_data) {
        return true;
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    async getUserDetails(user_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_user_details(user_id).then(
                (res) => {
                    var usrViewResp = new db_resp(res[0].tms_get_user_details);
                    if (!usrViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(usrViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    get_users_list_by_org_id_role_id(org_id, role_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_users_list_by_org_id_role_id(org_id, role_id).then(
                (res) => {
                    var usrViewResp = new db_resp(
                        res[0].tms_get_users_list_by_org_id_role_id
                    );
                    if (!usrViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                usrViewResp.data,
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getNotificationsFrUser(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            const usr_id = users_model.getUUID(this.userContext);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_notifications_fr_user(usr_id, query.pageSize).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_notifications_fr_user
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    updateNotificationsByUser(entry_id = 0, query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['entry_id'] = entry_id;
            var form_data = JSON.stringify(query);
            console.log('form_data', form_data);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_update_notification_by_user(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_update_notification_by_user
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    async getUserData(user_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_user_data(user_id).then(
                (res) => {
                    var usrViewResp = new db_resp(res[0].tms_get_user_data);
                    if (!usrViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(usrViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }
    getFreshInstance(model) {
        const clonedInstance = new user_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new user_model();
        return instance;
    }
}

module.exports = new user_model();
