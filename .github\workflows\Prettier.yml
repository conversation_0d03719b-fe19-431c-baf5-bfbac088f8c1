name: Prettier Checks

on:
    pull_request:
        types: [opened, synchronize, reopened]
        #types: [opened, edited, reopened]

# on:
#     push:
#         branches:
#             - main

# Cancel prev CI if new commit come
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
    # Detect changes in different folders
    detect-changes:
        runs-on: ubuntu-latest
        outputs:
            auth-changed: ${{ steps.changes.outputs.auth }}
            app-changed: ${{ steps.changes.outputs.app }}
            frontend-changed: ${{ steps.changes.outputs.frontend }}
        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Detect changes
              id: changes
              run: |
                  # Get the list of changed files
                  if [ "${{ github.event_name }}" = "pull_request" ]; then
                      # For pull requests, compare against the base branch
                      CHANGED_FILES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD)
                  else
                      # For push events, compare against the previous commit
                      CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
                  fi

                  echo "Changed files:"
                  echo "$CHANGED_FILES"
                  echo ""
                  echo "Repository structure:"
                  echo "- frontend/ (android-app, react-app1)"
                  echo "- backend/ (App, Auth, Serverless, forwarder, AppForwarder)"
                  echo ""

                  # Check backend/Auth changes
                  if echo "$CHANGED_FILES" | grep -q "^backend/Auth/"; then
                      echo "auth=true" >> $GITHUB_OUTPUT
                      echo "✓ Backend/Auth changes detected"
                  else
                      echo "auth=false" >> $GITHUB_OUTPUT
                      echo "✗ No Backend/Auth changes"
                  fi

                  # Check backend/App changes
                  if echo "$CHANGED_FILES" | grep -q "^backend/App/"; then
                      echo "app=true" >> $GITHUB_OUTPUT
                      echo "✓ Backend/App changes detected"
                  else
                      echo "app=false" >> $GITHUB_OUTPUT
                      echo "✗ No Backend/App changes"
                  fi

                  # Check frontend changes (any subfolder)
                  if echo "$CHANGED_FILES" | grep -q "^frontend/"; then
                      echo "frontend=true" >> $GITHUB_OUTPUT
                      echo "✓ Frontend changes detected"

                      # Show which frontend components changed
                      if echo "$CHANGED_FILES" | grep -q "^frontend/android-app/"; then
                          echo "  - Android app changes detected"
                      fi
                      if echo "$CHANGED_FILES" | grep -q "^frontend/react-app1/"; then
                          echo "  - React app changes detected"
                      fi
                  else
                      echo "frontend=false" >> $GITHUB_OUTPUT
                      echo "✗ No Frontend changes"
                  fi

    # Backend App Prettier job - only runs when backend/App changes are detected
    prettier-for-APP:
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.app-changed == 'true'
        defaults:
            run:
                working-directory: ./backend/App

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '16.14.0'

            - name: Install dependencies
              run: npm install

            - name: Run Prettier
              run: npx prettier --check '**/*.{css,js,json,jsx}'

    # Backend Auth Prettier job - only runs when backend/Auth changes are detected
    prettier-for-Auth:
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.auth-changed == 'true'
        defaults:
            run:
                working-directory: ./backend/Auth

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '16.14.0'

            - name: Install dependencies
              run: npm install

            - name: Run Prettier
              run: npx prettier --check '**/*.{css,js,json,jsx}'

    # Frontend Prettier job - only runs when frontend changes are detected
    prettier-for-Frontend:
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.frontend-changed == 'true'
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '16.14.0'

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=4096

            - name: Run Prettier
              run: npx prettier --write '**/*.{css,js,json,jsx}'

            - name: Run Prettier
              run: npx prettier --check '**/*.{css,js,json,jsx}'

    Status-Checks-For-Prettier:
        runs-on: ubuntu-latest
        needs:
            [
                detect-changes,
                prettier-for-APP,
                prettier-for-Auth,
                prettier-for-Frontend,
            ]
        if: ${{ always() }}
        steps:
            - name: Check Previous Jobs
              id: check-request
              run: |
                  echo "Checking Prettier job results based on detected changes..."

                  # Check prettier-for-Auth if it was supposed to run
                  if [[ ${{ needs.detect-changes.outputs.auth-changed }} == 'true' ]]; then
                    echo "Checking Auth Prettier job result..."
                    if [[ ${{ needs.prettier-for-Auth.result }} == 'failure' ]]; then
                      echo "❌ Auth Prettier checks failed."
                      exit 1
                    else
                      echo "✅ Auth Prettier checks passed."
                    fi
                  else
                    echo "⏭️ Auth Prettier job skipped (no changes detected)."
                  fi

                  # Check prettier-for-APP if it was supposed to run
                  if [[ ${{ needs.detect-changes.outputs.app-changed }} == 'true' ]]; then
                    echo "Checking App Prettier job result..."
                    if [[ ${{ needs.prettier-for-APP.result }} == 'failure' ]]; then
                      echo "❌ App Prettier checks failed."
                      exit 1
                    else
                      echo "✅ App Prettier checks passed."
                    fi
                  else
                    echo "⏭️ App Prettier job skipped (no changes detected)."
                  fi

                  # Check prettier-for-Frontend if it was supposed to run
                  if [[ ${{ needs.detect-changes.outputs.frontend-changed }} == 'true' ]]; then
                    echo "Checking Frontend Prettier job result..."
                    if [[ ${{ needs.prettier-for-Frontend.result }} == 'failure' ]]; then
                      echo "❌ Frontend Prettier checks failed."
                      exit 1
                    else
                      echo "✅ Frontend Prettier checks passed."
                    fi
                  else
                    echo "⏭️ Frontend Prettier job skipped (no changes detected)."
                  fi

                  echo "🎉 All required Prettier checks passed!"

            - name: Check Status
              run: echo "Failed"
              if: steps.check-request.outcome == 'Failure'
